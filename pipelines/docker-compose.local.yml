services:
  openwebui:
    image: ghcr.io/open-webui/open-webui:main
    ports:
      - "3000:8080"
    environment:
      - ENV=dev
    volumes:
      - open-webui:/app/backend/data
  pipelines:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - MINIMUM_BUILD=false
        - USE_CUDA=false
    ports:
      - "9099:9099"
    volumes:
      - ./pipelines:/app/pipelines
    restart: always
    environment:
      - CUSTOM_AUTH_ENDPOINT=http://openwebui:8080/api/v1/auth
      - CUSTOM_AUTH_TIMEOUT=10
      - PIPELINES_API_KEY=0p3n-w3bu!
volumes:
  open-webui:
  pipelines: