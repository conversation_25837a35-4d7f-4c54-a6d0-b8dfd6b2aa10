name: Merge Docker Images

on:
    workflow_call:
        inputs:
            cache_id:
                required: true
                type: string
            extract_flavor:
                required: false
                default: ""
                type: string
            extract_tags:
                required: false
                default: ""
                type: string
            image_name:
                required: true
                type: string
            registry:
                required: false
                default: ghcr.io
                type: string

env:
    FULL_IMAGE_NAME: ${{ inputs.registry }}/${{ inputs.image_name }}

jobs:
    merge-images:
        runs-on: ubuntu-latest
        steps:
            - name: Download digests
              uses: actions/download-artifact@v4
              with:
                  pattern: digests-${{ inputs.cache_id }}-*
                  path: /tmp/digests
                  merge-multiple: true

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3

            - name: Log in to the Container registry
              uses: docker/login-action@v3
              with:
                  registry: ${{ inputs.registry }}
                  username: ${{ github.actor }}
                  password: ${{ secrets.GITHUB_TOKEN }}

            - name: Extract metadata for Docker images
              id: meta
              uses: docker/metadata-action@v5
              with:
                  images: ${{ env.FULL_IMAGE_NAME }}
                  tags: |
                      type=ref,event=branch
                      type=ref,event=tag
                      type=sha,prefix=git-
                      ${{ inputs.extract_tags }}
                  flavor: |
                      latest=${{ github.ref == 'refs/heads/main' }}
                      ${{ inputs.extract_flavor }}

            - name: Create manifest list and push
              working-directory: /tmp/digests
              run: |
                  docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
                    $(printf '${{ env.FULL_IMAGE_NAME }}@sha256:%s ' *)

            - name: Inspect image
              run: |
                  docker buildx imagetools inspect ${{ env.FULL_IMAGE_NAME }}:${{ steps.meta.outputs.version }}
